.theme-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: 25px;
  background-color: var(--button-bg);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.theme-toggle:hover {
  background-color: var(--button-hover-bg);
  border-color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

.theme-toggle:active {
  transform: translateY(0);
}

.theme-toggle-icon {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.theme-toggle:hover .theme-toggle-icon {
  transform: rotate(20deg);
}

.theme-toggle-text {
  font-family: inherit;
}

@media (max-width: 480px) {
  .theme-toggle-text {
    display: none;
  }
  
  .theme-toggle {
    padding: 0.5rem;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    justify-content: center;
  }
}
