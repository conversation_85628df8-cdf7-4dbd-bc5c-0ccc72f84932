:root{font-family:system-ui,Avenir,Helvetica,Arial,sans-serif;line-height:1.5;font-weight:400;font-synthesis:none;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}:root,[data-theme=light]{--bg-color: #ffffff;--text-color: #213547;--text-secondary: #646cff;--border-color: #e0e0e0;--button-bg: #f9f9f9;--button-hover-bg: #e9e9e9;--card-bg: #ffffff;--accent-color: #646cff;--accent-hover: #747bff;--shadow-color: rgba(0, 0, 0, .1)}[data-theme=dark]{--bg-color: #242424;--text-color: rgba(255, 255, 255, .87);--text-secondary: #646cff;--border-color: #404040;--button-bg: #1a1a1a;--button-hover-bg: #2a2a2a;--card-bg: #1e1e1e;--accent-color: #646cff;--accent-hover: #535bf2;--shadow-color: rgba(0, 0, 0, .3)}body{margin:0;background-color:var(--bg-color);color:var(--text-color);transition:background-color .3s ease,color .3s ease;min-height:100vh}a{font-weight:500;color:var(--text-secondary);text-decoration:inherit;transition:color .3s ease}a:hover{color:var(--accent-hover)}h1{font-size:3.2em;line-height:1.1;color:var(--text-color)}button{border-radius:8px;border:1px solid var(--border-color);padding:.6em 1.2em;font-size:1em;font-weight:500;font-family:inherit;background-color:var(--button-bg);color:var(--text-color);cursor:pointer;transition:all .3s ease}button:hover{border-color:var(--accent-color);background-color:var(--button-hover-bg)}button:focus,button:focus-visible{outline:2px solid var(--accent-color);outline-offset:2px}#root{max-width:1280px;margin:0 auto;min-height:100vh;display:flex;flex-direction:column}.app{display:flex;flex-direction:column;min-height:100vh}.app-header{padding:1rem 2rem;display:flex;justify-content:flex-end;align-items:center;border-bottom:1px solid var(--border-color);background-color:var(--card-bg);transition:all .3s ease}.app-main{flex:1;display:flex;flex-direction:column;justify-content:center;align-items:center;padding:2rem;text-align:center}.logos{display:flex;justify-content:center;align-items:center;gap:2rem;margin-bottom:2rem}.logo{height:6em;padding:1.5em;will-change:filter;transition:filter .3s}.logo:hover{filter:drop-shadow(0 0 2em #646cffaa)}.logo.react:hover{filter:drop-shadow(0 0 2em #61dafbaa)}@keyframes logo-spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (prefers-reduced-motion: no-preference){a:nth-of-type(2) .logo{animation:logo-spin infinite 20s linear}}.card{padding:2em;background-color:var(--card-bg);border-radius:12px;border:1px solid var(--border-color);box-shadow:0 4px 6px var(--shadow-color);transition:all .3s ease;margin:2rem 0}.card:hover{transform:translateY(-2px);box-shadow:0 8px 12px var(--shadow-color)}.read-the-docs{color:var(--text-secondary);opacity:.8;transition:color .3s ease}@media (max-width: 768px){.app-header,.app-main{padding:1rem}.logos{gap:1rem;margin-bottom:1rem}.logo{height:4em;padding:1em}h1{font-size:2.5em}}.theme-toggle{display:flex;align-items:center;gap:.5rem;padding:.5rem 1rem;border:2px solid var(--border-color);border-radius:25px;background-color:var(--button-bg);color:var(--text-color);cursor:pointer;transition:all .3s ease;font-size:.9rem;font-weight:500}.theme-toggle:hover{background-color:var(--button-hover-bg);border-color:var(--accent-color);transform:translateY(-2px);box-shadow:0 4px 8px var(--shadow-color)}.theme-toggle:active{transform:translateY(0)}.theme-toggle-icon{font-size:1.2rem;transition:transform .3s ease}.theme-toggle:hover .theme-toggle-icon{transform:rotate(20deg)}.theme-toggle-text{font-family:inherit}@media (max-width: 480px){.theme-toggle-text{display:none}.theme-toggle{padding:.5rem;border-radius:50%;width:3rem;height:3rem;justify-content:center}}
